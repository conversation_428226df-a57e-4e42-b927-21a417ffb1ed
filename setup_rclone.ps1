# Google Drive Access Setup Script
# This script helps you set up rclone to access Google Drive via command line

Write-Host "Setting up rclone for Google Drive access..." -ForegroundColor Green

# Check if rclone is installed
if (!(Get-Command rclone -ErrorAction SilentlyContinue)) {
    Write-Host "Installing rclone..." -ForegroundColor Yellow
    
    # Download rclone
    $rcloneUrl = "https://downloads.rclone.org/rclone-current-windows-amd64.zip"
    $downloadPath = "$env:TEMP\rclone.zip"
    $extractPath = "$env:TEMP\rclone"
    
    Invoke-WebRequest -Uri $rcloneUrl -OutFile $downloadPath
    Expand-Archive -Path $downloadPath -DestinationPath $extractPath -Force
    
    # Find rclone.exe and copy to a permanent location
    $rcloneExe = Get-ChildItem -Path $extractPath -Name "rclone.exe" -Recurse | Select-Object -First 1
    $rclonePath = Join-Path $extractPath $rcloneExe.DirectoryName
    
    # Create rclone directory in user profile
    $userRclonePath = "$env:USERPROFILE\rclone"
    New-Item -Path $userRclonePath -ItemType Directory -Force
    Copy-Item -Path "$rclonePath\rclone.exe" -Destination $userRclonePath
    
    # Add to PATH for current session
    $env:PATH += ";$userRclonePath"
    
    Write-Host "Rclone installed successfully!" -ForegroundColor Green
} else {
    Write-Host "Rclone is already installed." -ForegroundColor Green
}

Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Run: rclone config" -ForegroundColor White
Write-Host "2. Choose 'n' for new remote" -ForegroundColor White
Write-Host "3. Name it 'gdrive' or any name you prefer" -ForegroundColor White
Write-Host "4. Choose '15' for Google Drive" -ForegroundColor White
Write-Host "5. Leave client_id and client_secret blank (press Enter)" -ForegroundColor White
Write-Host "6. Choose '1' for full access" -ForegroundColor White
Write-Host "7. Leave root_folder_id blank" -ForegroundColor White
Write-Host "8. Leave service_account_file blank" -ForegroundColor White
Write-Host "9. Choose 'n' for advanced config" -ForegroundColor White
Write-Host "10. Choose 'y' for auto config (this will open browser)" -ForegroundColor White
Write-Host "11. Choose 'n' for team drive" -ForegroundColor White
Write-Host "12. Choose 'y' to confirm" -ForegroundColor White
Write-Host "13. Choose 'q' to quit config" -ForegroundColor White
