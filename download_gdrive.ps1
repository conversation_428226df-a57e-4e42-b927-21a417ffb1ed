# Google Drive Download Script
# Use this script to download files from Google Drive using rclone

param(
    [Parameter(Mandatory=$false)]
    [string]$RemoteName = "gdrive",
    
    [Parameter(Mandatory=$false)]
    [string]$RemotePath = "",
    
    [Parameter(Mandatory=$false)]
    [string]$LocalPath = ".\downloads",
    
    [Parameter(Mandatory=$false)]
    [switch]$ListOnly
)

Write-Host "Google Drive Download Script" -ForegroundColor Green
Write-Host "==========================" -ForegroundColor Green

# Check if rclone is available
if (!(Get-Command rclone -ErrorAction SilentlyContinue)) {
    Write-Host "Error: rclone is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please run setup_rclone.ps1 first" -ForegroundColor Yellow
    exit 1
}

# Check if remote is configured
$remotes = rclone listremotes
if ($remotes -notcontains "$RemoteName`:") {
    Write-Host "Error: Remote '$RemoteName' is not configured" -ForegroundColor Red
    Write-Host "Please run 'rclone config' to set up your Google Drive connection" -ForegroundColor Yellow
    exit 1
}

if ($ListOnly) {
    Write-Host "Listing files in Google Drive..." -ForegroundColor Cyan
    rclone lsd "$RemoteName`:$RemotePath" --max-depth 1
    Write-Host "`nTo list files in a specific folder, use:" -ForegroundColor Yellow
    Write-Host "rclone ls `"$RemoteName`:folder_name`"" -ForegroundColor White
} else {
    # Create local directory if it doesn't exist
    if (!(Test-Path $LocalPath)) {
        New-Item -Path $LocalPath -ItemType Directory -Force
        Write-Host "Created directory: $LocalPath" -ForegroundColor Green
    }
    
    Write-Host "Downloading from Google Drive..." -ForegroundColor Cyan
    Write-Host "Remote: $RemoteName`:$RemotePath" -ForegroundColor White
    Write-Host "Local: $LocalPath" -ForegroundColor White
    
    # Download files
    rclone copy "$RemoteName`:$RemotePath" $LocalPath --progress --transfers 4
    
    Write-Host "`nDownload completed!" -ForegroundColor Green
}

Write-Host "`nUseful rclone commands:" -ForegroundColor Cyan
Write-Host "List folders: rclone lsd gdrive:" -ForegroundColor White
Write-Host "List files: rclone ls gdrive:" -ForegroundColor White
Write-Host "Download specific folder: rclone copy gdrive:folder_name .\downloads" -ForegroundColor White
Write-Host "Download specific file: rclone copy gdrive:file.txt .\downloads" -ForegroundColor White
Write-Host "Sync (two-way): rclone sync gdrive: .\sync_folder" -ForegroundColor White
