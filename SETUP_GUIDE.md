# Google Drive Access Setup Guide

## Overview
This guide will help you set up command-line access to your Google Drive files, bypassing web interface restrictions.

## What We've Created
1. **gdrive_downloader.py** - Main Python script to access Google Drive
2. **setup_gdrive.py** - Setup script (already run)
3. **credentials_template.json** - Template for your API credentials

## Step-by-Step Setup

### Step 1: Create Google API Credentials

Since your organization blocks the Google Drive website, you'll need to create API credentials using an alternative method:

#### Option A: Use Personal Device/Network
1. On a personal device or network, go to: https://console.developers.google.com/
2. Sign in with your Google account: **<EMAIL>**
3. Create a new project or select an existing one
4. Enable the Google Drive API:
   - Go to "APIs & Services" > "Library"
   - Search for "Google Drive API"
   - Click on it and press "Enable"
5. Create credentials:
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"
   - Choose "Desktop application"
   - Give it a name (e.g., "Drive Downloader")
   - Download the JSON file
6. Transfer the JSON file to your work computer (USB, email, etc.)
7. Rename it to `credentials.json` and place it in this directory

#### Option B: Use Mobile Hotspot
If available, use your mobile phone's hotspot to access the Google Console directly from your work computer.

### Step 2: Run the Downloader

Once you have `credentials.json` in place:

```bash
python gdrive_downloader.py
```

### Step 3: First-Time Authentication

1. The script will open a web browser for authentication
2. Sign in with your Google account
3. Grant permission to access your Drive
4. The browser will redirect to localhost (this completes the setup)
5. Your credentials will be saved for future use

## Using the Downloader

The script provides several options:

### 1. List All Files
- Shows your Google Drive files with their IDs
- Use this to find files you want to download

### 2. Search Files
- Search for files by name
- Example: search for "report" to find all files containing "report"

### 3. Download Single File
- Use the file ID from the list/search results
- Downloads to current directory or specified folder

### 4. Download Entire Folder
- Use folder ID to download all contents recursively
- Maintains folder structure

## File Types Supported

- **Regular files**: PDFs, images, videos, documents, etc.
- **Google Docs**: Exported as PDF
- **Google Sheets**: Exported as Excel (.xlsx)
- **Google Slides**: Exported as PDF

## Example Usage

```bash
# Run the downloader
python gdrive_downloader.py

# Choose option 1 to list files
# Find the file you want and note its ID
# Choose option 3 to download by ID
# Enter the file ID when prompted
```

## Troubleshooting

### "credentials.json not found"
- Make sure you've downloaded and renamed the credentials file correctly
- It should be in the same directory as the Python scripts

### "Authentication failed"
- Check your internet connection
- Make sure you're using the correct Google account
- Try deleting `token.pickle` and re-authenticating

### "Permission denied" errors
- Make sure you have write permissions in the download directory
- Try running as administrator if needed

### Network restrictions
- If the authentication browser redirect fails, try:
  - Using a different browser
  - Temporarily disabling firewall/antivirus
  - Using mobile hotspot for initial setup

## Alternative: Manual File IDs

If you can access Google Drive on another device:
1. Open Google Drive in a web browser
2. Right-click on files and select "Get link"
3. The file ID is the long string in the URL after `/d/` and before `/view`
4. Use these IDs directly in the downloader

## Security Notes

- Your credentials are stored locally in `credentials.json` and `token.pickle`
- Keep these files secure - they provide access to your Google Drive
- The script only requests read-only access to your Drive
- You can revoke access anytime from your Google Account settings

## Need Help?

If you encounter issues:
1. Check the error messages carefully
2. Ensure all files are in the correct directory
3. Verify your internet connection allows API access
4. Try the authentication process again

The Python approach bypasses web interface restrictions by using Google's official API, which is typically allowed even when the website is blocked.
