# Quick Commands for Google Drive Access

## Setup (one-time)
```powershell
# Run the setup script
.\setup_rclone.ps1

# Configure Google Drive connection
rclone config
```

## Basic Commands

### List your files and folders
```powershell
# List top-level folders
rclone lsd gdrive:

# List all files in root
rclone ls gdrive:

# List files in a specific folder
rclone ls "gdrive:My Documents"
```

### Download files
```powershell
# Download everything to local folder
.\download_gdrive.ps1

# Download specific folder
rclone copy "gdrive:Important Files" .\downloads

# Download specific file
rclone copy "gdrive:document.pdf" .\downloads

# Download with progress bar
rclone copy gdrive: .\all_files --progress
```

### Search for files
```powershell
# Find files by name
rclone ls gdrive: | findstr "filename"

# Find files by extension
rclone ls gdrive: | findstr "\.pdf"
```

### Advanced Options
```powershell
# Download only files modified in last 30 days
rclone copy gdrive: .\recent --max-age 30d

# Download files larger than 10MB
rclone copy gdrive: .\large_files --min-size 10M

# Exclude certain file types
rclone copy gdrive: .\downloads --exclude "*.tmp"
```

## Useful Flags
- `--progress` - Show transfer progress
- `--dry-run` - See what would be copied without actually doing it
- `--include "*.pdf"` - Only include PDF files
- `--exclude "*.tmp"` - Exclude temporary files
- `--max-age 7d` - Only files newer than 7 days
- `--transfers 8` - Use 8 parallel transfers (faster)

## Safety Tips
- Always use `--dry-run` first to see what will happen
- Use `rclone ls` to browse before downloading
- Create specific folders for downloads to stay organized
