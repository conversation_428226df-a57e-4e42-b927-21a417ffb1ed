#!/usr/bin/env python3
"""
Setup script for Google Drive Downloader
Installs required dependencies and helps create credentials
"""

import subprocess
import sys
import os
import json

def install_package(package):
    """Install a Python package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def check_package(package):
    """Check if a package is installed"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False

def create_credentials_template():
    """Create a template credentials.json file"""
    template = {
        "installed": *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    }
    
    with open('credentials_template.json', 'w') as f:
        json.dump(template, f, indent=2)
    
    print("Created credentials_template.json")
    print("Please:")
    print("1. Go to https://console.developers.google.com/")
    print("2. Create a new project or select existing one")
    print("3. Enable Google Drive API")
    print("4. Create OAuth 2.0 credentials (Desktop application)")
    print("5. Download the credentials JSON file")
    print("6. Rename it to 'credentials.json' in this directory")

def main():
    print("Google Drive Downloader Setup")
    print("=============================")
    
    # Required packages
    packages = [
        ('google-api-python-client', 'googleapiclient'),
        ('google-auth-httplib2', 'google.auth'),
        ('google-auth-oauthlib', 'google_auth_oauthlib')
    ]
    
    print("\nChecking and installing required packages...")
    
    for pip_name, import_name in packages:
        if check_package(import_name):
            print(f"✓ {pip_name} is already installed")
        else:
            print(f"Installing {pip_name}...")
            if install_package(pip_name):
                print(f"✓ {pip_name} installed successfully")
            else:
                print(f"✗ Failed to install {pip_name}")
                print("You may need to run this script as administrator or use:")
                print(f"pip install {pip_name}")
    
    print("\nSetup complete!")
    
    # Check for credentials file
    if not os.path.exists('credentials.json'):
        print("\nCredentials file not found.")
        create_template = input("Create credentials template? (y/n): ").lower().strip()
        if create_template == 'y':
            create_credentials_template()
    else:
        print("\n✓ credentials.json found")
    
    print("\nTo use the Google Drive Downloader:")
    print("1. Make sure you have credentials.json in this directory")
    print("2. Run: python gdrive_downloader.py")

if __name__ == "__main__":
    main()
