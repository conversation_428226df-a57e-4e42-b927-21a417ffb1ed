#!/usr/bin/env python3
"""
Google Drive File Downloader
A Python script to download files from Google Drive using the API
Bypasses web interface restrictions
"""

import os
import io
import json
import pickle
from pathlib import Path
from googleapiclient.discovery import build
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from googleapiclient.http import MediaIoBaseDownload

# If modifying these scopes, delete the file token.pickle.
SCOPES = ['https://www.googleapis.com/auth/drive.readonly']

class GoogleDriveDownloader:
    def __init__(self):
        self.service = None
        self.authenticate()
    
    def authenticate(self):
        """Authenticate with Google Drive API"""
        creds = None
        # The file token.pickle stores the user's access and refresh tokens.
        if os.path.exists('token.pickle'):
            with open('token.pickle', 'rb') as token:
                creds = pickle.load(token)
        
        # If there are no (valid) credentials available, let the user log in.
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                creds.refresh(Request())
            else:
                if not os.path.exists('credentials.json'):
                    print("ERROR: credentials.json file not found!")
                    print("Please create Google API credentials first.")
                    print("Visit: https://console.developers.google.com/")
                    return False
                
                flow = InstalledAppFlow.from_client_secrets_file(
                    'credentials.json', SCOPES)
                creds = flow.run_local_server(port=0)
            
            # Save the credentials for the next run
            with open('token.pickle', 'wb') as token:
                pickle.dump(creds, token)
        
        self.service = build('drive', 'v3', credentials=creds)
        return True
    
    def list_files(self, folder_id=None, query=None):
        """List files in Google Drive"""
        if not self.service:
            return []
        
        try:
            # Build query
            search_query = ""
            if folder_id:
                search_query = f"'{folder_id}' in parents"
            if query:
                if search_query:
                    search_query += f" and {query}"
                else:
                    search_query = query
            
            results = self.service.files().list(
                q=search_query,
                pageSize=100,
                fields="nextPageToken, files(id, name, size, mimeType, parents)"
            ).execute()
            
            items = results.get('files', [])
            return items
        except Exception as e:
            print(f"Error listing files: {e}")
            return []
    
    def download_file(self, file_id, file_name, destination_folder="."):
        """Download a file from Google Drive"""
        if not self.service:
            return False
        
        try:
            # Create destination folder if it doesn't exist
            Path(destination_folder).mkdir(parents=True, exist_ok=True)
            
            # Get file metadata
            file_metadata = self.service.files().get(fileId=file_id).execute()
            mime_type = file_metadata.get('mimeType', '')
            
            # Handle Google Workspace files (Docs, Sheets, Slides)
            if mime_type.startswith('application/vnd.google-apps'):
                return self.export_google_file(file_id, file_name, mime_type, destination_folder)
            
            # Download regular files
            request = self.service.files().get_media(fileId=file_id)
            file_path = os.path.join(destination_folder, file_name)
            
            with io.FileIO(file_path, 'wb') as fh:
                downloader = MediaIoBaseDownload(fh, request)
                done = False
                while done is False:
                    status, done = downloader.next_chunk()
                    if status:
                        print(f"Download progress: {int(status.progress() * 100)}%")
            
            print(f"Downloaded: {file_name}")
            return True
            
        except Exception as e:
            print(f"Error downloading {file_name}: {e}")
            return False
    
    def export_google_file(self, file_id, file_name, mime_type, destination_folder):
        """Export Google Workspace files to common formats"""
        export_formats = {
            'application/vnd.google-apps.document': ('application/pdf', '.pdf'),
            'application/vnd.google-apps.spreadsheet': ('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', '.xlsx'),
            'application/vnd.google-apps.presentation': ('application/pdf', '.pdf'),
        }
        
        if mime_type not in export_formats:
            print(f"Cannot export file type: {mime_type}")
            return False
        
        export_mime_type, extension = export_formats[mime_type]
        
        try:
            request = self.service.files().export_media(fileId=file_id, mimeType=export_mime_type)
            file_path = os.path.join(destination_folder, f"{file_name}{extension}")
            
            with io.FileIO(file_path, 'wb') as fh:
                downloader = MediaIoBaseDownload(fh, request)
                done = False
                while done is False:
                    status, done = downloader.next_chunk()
                    if status:
                        print(f"Export progress: {int(status.progress() * 100)}%")
            
            print(f"Exported: {file_name}{extension}")
            return True
            
        except Exception as e:
            print(f"Error exporting {file_name}: {e}")
            return False
    
    def download_folder(self, folder_id, destination_folder):
        """Download all files in a folder recursively"""
        files = self.list_files(folder_id=folder_id)
        
        for file in files:
            file_id = file['id']
            file_name = file['name']
            mime_type = file['mimeType']
            
            if mime_type == 'application/vnd.google-apps.folder':
                # Create subfolder and download recursively
                subfolder_path = os.path.join(destination_folder, file_name)
                self.download_folder(file_id, subfolder_path)
            else:
                # Download file
                self.download_file(file_id, file_name, destination_folder)

def main():
    print("Google Drive Downloader")
    print("======================")
    
    downloader = GoogleDriveDownloader()
    
    if not downloader.service:
        print("Failed to authenticate with Google Drive")
        return
    
    while True:
        print("\nOptions:")
        print("1. List all files")
        print("2. Search files")
        print("3. Download file by ID")
        print("4. Download folder by ID")
        print("5. Exit")
        
        choice = input("\nEnter your choice (1-5): ").strip()
        
        if choice == '1':
            print("\nListing all files...")
            files = downloader.list_files()
            for i, file in enumerate(files[:20], 1):  # Show first 20 files
                size = file.get('size', 'N/A')
                print(f"{i}. {file['name']} (ID: {file['id']}) - Size: {size} bytes")
            if len(files) > 20:
                print(f"... and {len(files) - 20} more files")
        
        elif choice == '2':
            search_term = input("Enter search term: ").strip()
            query = f"name contains '{search_term}'"
            files = downloader.list_files(query=query)
            for i, file in enumerate(files, 1):
                size = file.get('size', 'N/A')
                print(f"{i}. {file['name']} (ID: {file['id']}) - Size: {size} bytes")
        
        elif choice == '3':
            file_id = input("Enter file ID: ").strip()
            file_name = input("Enter file name (or press Enter to auto-detect): ").strip()
            
            if not file_name:
                try:
                    file_metadata = downloader.service.files().get(fileId=file_id).execute()
                    file_name = file_metadata['name']
                except:
                    file_name = f"file_{file_id}"
            
            destination = input("Enter destination folder (or press Enter for current): ").strip() or "."
            downloader.download_file(file_id, file_name, destination)
        
        elif choice == '4':
            folder_id = input("Enter folder ID: ").strip()
            destination = input("Enter destination folder (or press Enter for current): ").strip() or "."
            downloader.download_folder(folder_id, destination)
        
        elif choice == '5':
            print("Goodbye!")
            break
        
        else:
            print("Invalid choice. Please try again.")

if __name__ == "__main__":
    main()
