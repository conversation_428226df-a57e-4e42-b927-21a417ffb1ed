#!/usr/bin/env python3
"""
Quick test script to verify Google Drive API setup
"""

import os

def check_setup():
    print("Google Drive Setup Checker")
    print("=" * 30)
    
    # Check if required files exist
    files_to_check = [
        'gdrive_downloader.py',
        'setup_gdrive.py',
        'credentials_template.json'
    ]
    
    print("\n1. Checking required files:")
    for file in files_to_check:
        if os.path.exists(file):
            print(f"   ✓ {file}")
        else:
            print(f"   ✗ {file} - MISSING")
    
    # Check for credentials
    print("\n2. Checking credentials:")
    if os.path.exists('credentials.json'):
        print("   ✓ credentials.json found")
        credentials_ready = True
    else:
        print("   ✗ credentials.json not found")
        print("   → You need to create Google API credentials")
        credentials_ready = False
    
    # Check for token (previous authentication)
    if os.path.exists('token.pickle'):
        print("   ✓ token.pickle found (previously authenticated)")
    else:
        print("   ○ token.pickle not found (first-time setup)")
    
    # Check Python packages
    print("\n3. Checking Python packages:")
    packages = [
        'googleapiclient',
        'google.auth',
        'google_auth_oauthlib'
    ]
    
    all_packages_ok = True
    for package in packages:
        try:
            __import__(package)
            print(f"   ✓ {package}")
        except ImportError:
            print(f"   ✗ {package} - NOT INSTALLED")
            all_packages_ok = False
    
    # Summary and next steps
    print("\n" + "=" * 30)
    print("SUMMARY:")
    
    if not all_packages_ok:
        print("❌ Python packages missing - run: python setup_gdrive.py")
    elif not credentials_ready:
        print("⚠️  Setup incomplete - you need to create credentials.json")
        print("   Follow the instructions in SETUP_GUIDE.md")
    else:
        print("✅ Setup looks good! You can run: python gdrive_downloader.py")
    
    print("\nNext steps:")
    if not credentials_ready:
        print("1. Create Google API credentials (see SETUP_GUIDE.md)")
        print("2. Save as 'credentials.json' in this directory")
        print("3. Run: python gdrive_downloader.py")
    else:
        print("1. Run: python gdrive_downloader.py")
        print("2. Follow the authentication prompts")
        print("3. Start downloading your files!")

if __name__ == "__main__":
    check_setup()
